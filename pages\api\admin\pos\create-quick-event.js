import { supabase } from '@/lib/supabase'
import { verifyAdminAuth } from '@/lib/auth-utils'

/**
 * API endpoint for creating quick event transactions
 * POST /api/admin/pos/create-quick-event
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(req)
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error })
    }

    const { service, tier, payment } = req.body

    // Validate required fields
    if (!service?.id || !service?.name) {
      return res.status(400).json({ error: 'Service information is required' })
    }

    if (!tier?.id || !tier?.name || !tier?.duration || tier.price === undefined) {
      return res.status(400).json({ error: 'Tier information is required' })
    }

    if (!payment?.method || !payment?.amount || !payment?.currency) {
      return res.status(400).json({ error: 'Payment information is required' })
    }

    console.log('🚀 Creating quick event transaction:', {
      service: service.name,
      tier: tier.name,
      amount: payment.amount,
      method: payment.method
    })

    // Generate transaction ID
    const transactionId = `quick_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

    // Create quick event record in database
    const { data: quickEventData, error: quickEventError } = await supabase
      .from('quick_events')
      .insert([
        {
          transaction_id: transactionId,
          service_id: service.id,
          service_name: service.name,
          tier_id: tier.id,
          tier_name: tier.name,
          duration: tier.duration,
          amount: payment.amount,
          currency: payment.currency,
          payment_method: payment.method,
          payment_status: 'completed',
          payment_details: payment.details || null,
          created_by: authResult.user.id,
          created_at: new Date().toISOString()
        }
      ])
      .select()

    if (quickEventError) {
      console.error('❌ Error creating quick event record:', quickEventError)
      return res.status(500).json({ error: 'Failed to create quick event record' })
    }

    // Create payment record for consistency with full bookings
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .insert([
        {
          transaction_id: transactionId,
          amount: payment.amount,
          currency: payment.currency,
          payment_method: payment.method,
          payment_status: 'completed',
          payment_date: new Date().toISOString(),
          notes: `Quick Event: ${service.name} - ${tier.name}`,
          metadata: {
            type: 'quick_event',
            service_id: service.id,
            tier_id: tier.id,
            duration: tier.duration
          }
        }
      ])
      .select()

    if (paymentError) {
      console.error('❌ Error creating payment record:', paymentError)
      // Don't fail the request if payment record creation fails
      // The quick event record is the primary record
    }

    console.log('✅ Quick event transaction created successfully:', {
      transactionId,
      quickEventId: quickEventData[0]?.id,
      paymentId: paymentData?.[0]?.id
    })

    // Return success response
    res.status(201).json({
      success: true,
      transaction: {
        id: transactionId,
        quickEventId: quickEventData[0]?.id,
        paymentId: paymentData?.[0]?.id,
        service: service.name,
        tier: tier.name,
        amount: payment.amount,
        currency: payment.currency,
        method: payment.method,
        status: 'completed',
        createdAt: quickEventData[0]?.created_at
      }
    })

  } catch (error) {
    console.error('❌ Quick event creation error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}
