import { useState, useEffect } from 'react'
import Head from 'next/head'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ServiceTileGrid from '@/components/admin/pos/ServiceTileGrid'
import ServiceBookingAvailability from '@/components/admin/pos/ServiceBookingAvailability'
import POSCheckout from '@/components/admin/pos/POSCheckout'
import QuickEventModeToggle from '@/components/admin/pos/QuickEventModeToggle'
import QuickEventServiceSelector from '@/components/admin/pos/QuickEventServiceSelector'
import QuickEventPayment from '@/components/admin/pos/QuickEventPayment'
import SquareDebugger from '@/components/admin/pos/SquareDebugger'
import POSProductionDebugger from '@/components/admin/pos/POSProductionDebugger'
import { supabase } from '@/lib/supabase'
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils'
import { startPOSSessionMonitoring, stopPOSSessionMonitoring } from '@/lib/pos-session-manager'
import { initializePOSAuthProtection } from '@/lib/pos-auth-protection'
import { useAuth } from '@/contexts/AuthContext'
import '@/lib/console-filter' // Activate global console filtering
import styles from '@/styles/admin/POS.module.css'

export default function POSTerminal() {
  const [currentStep, setCurrentStep] = useState('services') // 'services', 'availability', 'checkout'
  const [selectedService, setSelectedService] = useState(null)
  const [selectedArtist, setSelectedArtist] = useState(null)
  const [selectedTier, setSelectedTier] = useState(null)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null)
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dashboardStats, setDashboardStats] = useState({
    todayBookings: 0,
    availableSlots: 0,
    revenue: 0
  })

  // Quick Event Mode state - Default to Quick Event Mode
  const [posMode, setPosMode] = useState('quick') // 'quick' or 'full' - Quick Event is default
  const [quickEventStep, setQuickEventStep] = useState('service') // 'service', 'payment'
  const [isProcessing, setIsProcessing] = useState(false)

  // Get auth context at component level (not inside useEffect)
  const { loading: authLoading } = useAuth()

  // Initialize POS with auth protection and data loading
  useEffect(() => {
    console.log('🏪 POS Terminal initializing with enhanced auth protection...')

    // Initialize POS authentication protection first
    const cleanupAuthProtection = initializePOSAuthProtection()

    // Fetch services data
    fetchServices()

    // Cleanup on unmount
    return () => {
      console.log('🏪 POS Terminal cleaning up...')
      stopPOSSessionMonitoring()
      cleanupAuthProtection()
    }
  }, [])

  // Start POS session monitoring when auth context is ready
  useEffect(() => {
    if (!authLoading) {
      console.log('🔄 Starting POS session monitoring after auth context initialization...')
      startPOSSessionMonitoring()
    }
  }, [authLoading])

  const fetchServices = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🏪 Fetching services with available artists for POS Terminal...')

      // Use the new API endpoint that includes artist availability
      const response = await fetch('/api/admin/pos/services-with-artists')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      console.log(`✅ Loaded ${data.services?.length || 0} services with artists`)
      setServices(data.services || [])

      // Also fetch dashboard stats
      fetchDashboardStats()
    } catch (err) {
      console.error('❌ Error fetching services with artists:', err)
      setError('Failed to load services. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Fetch dashboard statistics
  const fetchDashboardStats = async () => {
    try {
      console.log('📊 Fetching dashboard stats...')

      const today = new Date().toISOString().split('T')[0]

      // Fetch today's bookings
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('total_amount, status')
        .gte('created_at', `${today}T00:00:00`)
        .lt('created_at', `${today}T23:59:59`)

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError)
        return
      }

      // Calculate stats
      const todayBookings = bookingsData?.length || 0
      const revenue = bookingsData?.reduce((sum, booking) => {
        return sum + (parseFloat(booking.total_amount) || 0)
      }, 0) || 0

      // Mock available slots calculation (would be based on actual availability logic)
      const availableSlots = Math.max(0, 20 - todayBookings)

      setDashboardStats({
        todayBookings,
        availableSlots,
        revenue
      })

      console.log('✅ Dashboard stats updated:', { todayBookings, availableSlots, revenue })
    } catch (error) {
      console.error('❌ Error fetching dashboard stats:', error)
      // Keep default stats on error
    }
  }

  const handleServiceSelect = (service) => {
    console.log('🎯 Service selected:', service.name, 'Available artists:', service.availableArtistCount)
    setSelectedService(service)
    setSelectedArtist(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)
    setCurrentStep('availability')
  }

  const handleBookingSlotSelect = (artist, tier, timeSlot) => {
    console.log('📅 Booking slot selected:', { artist: artist.name, tier: tier.name, timeSlot })
    setSelectedArtist(artist)
    setSelectedTier(tier)
    setSelectedTimeSlot(timeSlot)
    setCurrentStep('checkout')
  }

  const handleBackToServices = () => {
    setSelectedService(null)
    setSelectedArtist(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)
    setCurrentStep('services')
  }

  const handleBackToAvailability = () => {
    setSelectedArtist(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)
    setCurrentStep('availability')
  }

  const handleTransactionComplete = () => {
    // Reset to services view after successful transaction
    setSelectedService(null)
    setSelectedArtist(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)

    if (posMode === 'quick') {
      setQuickEventStep('service')
    } else {
      setCurrentStep('services')
    }

    // Refresh dashboard stats after successful transaction
    fetchDashboardStats()
  }

  // Quick Event Mode handlers
  const handleModeToggle = (mode) => {
    setPosMode(mode)
    // Reset all selections when switching modes
    setSelectedService(null)
    setSelectedArtist(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)

    if (mode === 'quick') {
      setQuickEventStep('service')
    } else {
      setCurrentStep('services')
    }
  }

  const handleQuickServiceSelect = (service, tier) => {
    console.log('⚡ Quick Event service selected:', service.name, 'Tier:', tier.name)
    setSelectedService(service)
    setSelectedTier(tier)
    setQuickEventStep('payment')
  }

  const handleQuickEventBack = () => {
    if (quickEventStep === 'payment') {
      setQuickEventStep('service')
      setSelectedService(null)
      setSelectedTier(null)
    }
  }

  const renderStepIndicator = () => {
    if (posMode === 'quick') {
      return (
        <div className={styles.stepIndicator}>
          <div className={`${styles.step} ${quickEventStep === 'service' ? styles.active : ''}`}>
            <span className={styles.stepNumber}>1</span>
            <span className={styles.stepLabel}>Select Service</span>
          </div>
          <div className={`${styles.step} ${quickEventStep === 'payment' ? styles.active : ''}`}>
            <span className={styles.stepNumber}>2</span>
            <span className={styles.stepLabel}>Payment</span>
          </div>
        </div>
      )
    }

    return (
      <div className={styles.stepIndicator}>
        <div className={`${styles.step} ${currentStep === 'services' ? styles.active : ''}`}>
          <span className={styles.stepNumber}>1</span>
          <span className={styles.stepLabel}>Select Service</span>
        </div>
        <div className={`${styles.step} ${currentStep === 'availability' ? styles.active : ''}`}>
          <span className={styles.stepNumber}>2</span>
          <span className={styles.stepLabel}>Choose Time Slot</span>
        </div>
        <div className={`${styles.step} ${currentStep === 'checkout' ? styles.active : ''}`}>
          <span className={styles.stepNumber}>3</span>
          <span className={styles.stepLabel}>Checkout</span>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal">
          <div className={styles.posContainer}>
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading services...</p>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal">
          <div className={styles.posContainer}>
            <div className={styles.error}>
              <h3>Error Loading POS Terminal</h3>
              <p>{error}</p>
              <button
                className={styles.retryButton}
                onClick={fetchServices}
              >
                Retry
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <Head>
        {/* Console monitoring removed for production security */}
      </Head>
      <AdminLayout title="">{/* Remove redundant title to prevent double headers */}
        <div className={styles.posContainer}>
          <div className={styles.posHeader}>
            <div className={styles.headerLeft}>
              <div className={styles.headerTitleSection}>
                <h1 className={styles.posTitle}>Point of Sale Terminal</h1>
                <p className={styles.posSubtitle}>
                  {posMode === 'quick' ? 'Quick Event Service Processing' : 'Festival & Event Service Booking'}
                </p>
              </div>
              <div className={styles.headerControlsSection}>
                <QuickEventModeToggle
                  currentMode={posMode}
                  onModeChange={handleModeToggle}
                  disabled={loading || isProcessing}
                />
                {renderStepIndicator()}
              </div>
            </div>
            <div className={styles.headerRight}>
              <div className={styles.quickStats}>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{safeRender(dashboardStats.todayBookings, '0')}</div>
                  <div className={styles.statLabel}>Today's Bookings</div>
                </div>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{safeRender(dashboardStats.availableSlots, '0')}</div>
                  <div className={styles.statLabel}>Available Slots</div>
                </div>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{safeFormatCurrency(dashboardStats.revenue, 'AUD')}</div>
                  <div className={styles.statLabel}>Today's Revenue</div>
                </div>
              </div>
              <button
                className={styles.refreshButton}
                onClick={fetchDashboardStats}
                title="Refresh dashboard stats"
              >
                🔄 Refresh
              </button>
            </div>
          </div>

          <div className={styles.posContent}>
            {posMode === 'full' && (
              <>
                {currentStep === 'services' && (
                  <ServiceTileGrid
                    services={services}
                    onServiceSelect={handleServiceSelect}
                  />
                )}

                {currentStep === 'availability' && selectedService && (
                  <ServiceBookingAvailability
                    service={selectedService}
                    onBookingSlotSelect={handleBookingSlotSelect}
                    onBack={handleBackToServices}
                  />
                )}

                {currentStep === 'checkout' && selectedService && selectedArtist && selectedTier && selectedTimeSlot && (
                  <POSCheckout
                    service={selectedService}
                    artist={selectedArtist}
                    tier={selectedTier}
                    timeSlot={selectedTimeSlot}
                    onBack={handleBackToAvailability}
                    onComplete={handleTransactionComplete}
                  />
                )}
              </>
            )}

            {posMode === 'quick' && (
              <>
                {quickEventStep === 'service' && (
                  <QuickEventServiceSelector
                    services={services}
                    onServiceSelect={handleQuickServiceSelect}
                  />
                )}

                {quickEventStep === 'payment' && selectedService && selectedTier && (
                  <QuickEventPayment
                    service={selectedService}
                    tier={selectedTier}
                    onBack={handleQuickEventBack}
                    onComplete={handleTransactionComplete}
                  />
                )}
              </>
            )}
          </div>

          {/* Debug components */}
          {process.env.NODE_ENV === 'development' && <SquareDebugger />}
          <POSProductionDebugger />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
