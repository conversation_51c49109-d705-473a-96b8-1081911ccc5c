/* Enhanced POS Terminal Styles - Full Viewport Override */
.posContainer {
  /* Override AdminLayout constraints */
  position: fixed;
  top: 0;
  left: 70px; /* Account for collapsed sidebar */
  right: 0;
  bottom: 0;
  padding: 0.5rem;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 5; /* Below modals but above normal content */
}

/* Compact Header with Dashboard Elements - Touch Optimized */
.posHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
  position: relative;
  overflow: hidden;
  min-height: 60px;
  flex-shrink: 0;
}

.posHeader::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.headerLeft {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.headerTitleSection {
  flex-shrink: 0;
}

.headerControlsSection {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.posTitle {
  font-size: 1.75rem;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.posSubtitle {
  font-size: 0.95rem;
  margin: 0;
  opacity: 0.9;
}

.headerRight {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  z-index: 1;
}

.quickStats {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0;
}

.statItem {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  text-align: center;
  backdrop-filter: blur(10px);
  min-width: 60px;
}

.statValue {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

.statLabel {
  font-size: 0.7rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.1;
}

/* Compact Step Indicator */
.stepIndicator {
  display: flex;
  gap: 1rem;
  margin: 0;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.step.active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.02);
}

.stepNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  font-weight: 600;
  font-size: 0.7rem;
}

.step.active .stepNumber {
  background: white;
  color: #4ECDC4;
}

.stepLabel {
  font-weight: 500;
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Content Area - Viewport Constrained for Touch Devices */
.posContent {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex shrinking */
}

/* Enhanced Service Grid - Touch Optimized with Category Organization */
.serviceGrid {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

/* Full Booking Mode Header - Touch Optimized */
.fullBookingHeader {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
  flex-shrink: 0;
  padding: 0.5rem 0;
}

.fullBookingTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.fullBookingSubtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.backToCategoriesButton {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backToCategoriesButton:hover {
  background: #e9ecef;
  border-color: #4ECDC4;
  color: #4ECDC4;
}

/* Service Grid Container - Touch Optimized with Viewport Constraints */
.serviceGridContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  flex: 1;
  align-content: start;
  overflow-y: auto;
  padding-bottom: 0.5rem;
  min-height: 0;
}

.serviceTile {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  min-height: 120px; /* Touch-friendly minimum size */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.serviceTile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.serviceTile:hover {
  border-color: #4ECDC4;
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.serviceTile:hover::before {
  transform: scaleX(1);
}

.serviceTile.selected {
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.3);
}

.serviceTile.selected::before {
  transform: scaleX(1);
}

.serviceIcon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  display: block;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.serviceName {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.serviceDescription {
  font-size: 0.95rem;
  opacity: 0.8;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.servicePriceRange {
  font-size: 1.2rem;
  font-weight: 700;
  color: #4ECDC4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.serviceTile.selected .servicePriceRange {
  color: white;
}

.tierCount {
  font-size: 0.85rem;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.serviceTile.selected .tierCount {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.serviceAvailability {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.serviceAvailability.available {
  background: #4caf50;
}

.serviceAvailability.busy {
  background: #ff9800;
}

.serviceAvailability.unavailable {
  background: #f44336;
}

.noServices {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.noServices h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.noServices p {
  font-size: 1rem;
  line-height: 1.5;
}

.debugInfo {
  font-size: 0.8rem;
  color: #999;
  margin-top: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-family: monospace;
}

/* Tier Selection */
.tierSelector {
  padding: 2rem;
}

.tierHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.tierServiceName {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.tierSubtitle {
  font-size: 1.1rem;
  color: #666;
}

.tierGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.tierCard {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.tierCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.tierCard.recommended {
  border-color: #4ECDC4;
  position: relative;
}

.tierCard.recommended::before {
  content: 'Recommended';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #4ECDC4;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tierName {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.tierDuration {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1rem;
}

.tierPrice {
  font-size: 2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 1rem;
}

.tierDescription {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* Artist Selector Styles */
.artistSelector {
  padding: 2rem;
}

.selectorHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.selectorTitle {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.selectorSubtitle {
  font-size: 1.1rem;
  color: #666;
}

.artistGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.artistCard {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.artistCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.artistCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.artistCard:hover::before {
  transform: scaleX(1);
}

.artistCard.selected {
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.3);
}

.artistCard.selected::before {
  transform: scaleX(1);
}

.anyArtistCard {
  border-style: dashed;
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.anyArtistCard.selected {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border-style: solid;
}

.artistAvatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.artistInitials {
  font-size: 1.2rem;
  font-weight: 700;
}

.anyArtistIcon {
  font-size: 1.8rem;
}

.artistInfo {
  flex: 1;
  min-width: 0;
}

.artistName {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.artistCard.selected .artistName {
  color: white;
}

.artistSpecialty {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.artistCard.selected .artistSpecialty {
  color: rgba(255, 255, 255, 0.9);
}

.artistSkillLevel {
  margin-bottom: 0.5rem;
}

.skillBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.primaryServiceBadge {
  display: inline-block;
}

.badgeText {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.artistCard.selected .badgeText {
  background: rgba(255, 255, 255, 0.2);
}

.artistRate {
  text-align: right;
  flex-shrink: 0;
}

.rateText {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.artistCard.selected .rateText {
  color: rgba(255, 255, 255, 0.8);
}

.rateAmount {
  font-size: 1.1rem;
  font-weight: 700;
  color: #4ECDC4;
}

.artistCard.selected .rateAmount {
  color: white;
}

.noArtistsMessage {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.noArtistsMessage h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.noArtistsMessage p {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.selectionInfo {
  text-align: center;
  color: #666;
}

.selectionInfo p {
  margin: 0;
  font-size: 0.95rem;
}

.artistAvailability {
  font-size: 0.85rem;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  display: inline-block;
  font-weight: 500;
  border: 1px solid #e9ecef;
  margin-bottom: 0.5rem;
}

.serviceTile.selected .artistAvailability {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Navigation Buttons */
.navigationButtons {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.backButton {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.continueButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continueButton:hover {
  background: #40b0a8;
  transform: translateY(-1px);
}

.continueButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Quick Event Mode Toggle Styles */
.modeToggleContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0;
}

.modeToggleLabel {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.modeIcon {
  font-size: 1rem;
}

.modeToggle {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.25rem;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modeToggle.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.modeOption {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: center;
  touch-action: manipulation;
  font-size: 0.8rem;
}

.modeOption:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-1px);
}

.modeOption.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.modeOption.animating {
  transform: scale(0.95);
}

.modeOptionIcon {
  font-size: 1rem;
  margin-right: 0.25rem;
}

.modeOptionText {
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.modeOptionSubtext {
  display: none; /* Hide subtext in compact mode */
}

.modeDescription {
  display: none; /* Hide description in compact header */
}

/* Quick Event Service Selector - Touch Optimized */
.quickEventSelector {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.quickEventHeader {
  text-align: center;
  margin-bottom: 1rem;
  flex-shrink: 0;
  position: relative;
}

.quickEventTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.quickEventSubtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.backToCategoriesButton {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backToCategoriesButton:hover {
  background: #5a6268;
  transform: translateY(-50%) translateY(-1px);
}

/* Category Grid - Touch Optimized First Tier Navigation */
.categoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  width: 100%;
  flex: 1;
  align-content: start;
  overflow-y: auto;
  padding-bottom: 0.5rem;
  min-height: 0;
}

.categoryButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 100px; /* Touch-friendly minimum */
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.categoryButton:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.categoryButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.2);
}

.categoryIcon {
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.categoryName {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

.categoryCount {
  font-size: 0.8rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* Service Grid - Touch Optimized Second Tier Navigation */
.quickServiceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  width: 100%;
  flex: 1;
  align-content: start;
  overflow-y: auto;
  padding-bottom: 0.5rem;
  min-height: 0;
}

.quickServiceCard {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: fit-content;
}

.quickServiceCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.quickServiceHeader {
  text-align: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
}

.quickServiceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.quickTierGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.quickTierButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.4rem;
  padding: 1rem 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  min-height: 100px;
  justify-content: center;
  font-size: 0.9rem;
}

.quickTierButton:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.quickTierButton.recommended {
  border-color: #4ECDC4;
  position: relative;
}

.quickTierButton.recommended::before {
  content: 'Popular';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #4ECDC4;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.quickTierButton.selected {
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.3);
}

.quickTierName {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  line-height: 1.2;
}

.quickTierDuration {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.3rem;
}

.quickTierPrice {
  font-size: 1.1rem;
  font-weight: 700;
  color: #4ECDC4;
}

.quickTierButton.selected .quickTierPrice {
  color: white;
}

.quickTierDescription {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.3rem;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.noQuickServices {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.noServicesIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.noQuickServices h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.noQuickServices p {
  font-size: 1rem;
  line-height: 1.5;
}

/* Quick Event Payment Styles */
.quickEventPayment {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.quickPaymentHeader {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.quickPaymentTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.quickPaymentSummary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.serviceSummary {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
}

.serviceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.tierName {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.tierDuration {
  font-size: 0.8rem;
  color: #888;
}

.totalAmount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.amountLabel {
  font-size: 0.9rem;
  color: #666;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4ECDC4;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  color: #c53030;
  margin-bottom: 1.5rem;
}

.errorIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.errorRetry {
  background: #c53030;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-left: auto;
}

.errorRetry:hover {
  background: #9c2626;
}

.quickPaymentActions {
  display: flex;
  justify-content: flex-start;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
  margin-top: 2rem;
}

/* Sidebar State Responsive Handling */
.posContainer.expandedSidebar {
  left: 250px; /* Account for expanded sidebar */
}

/* Mobile & Tablet Responsive - Touch Optimized */
@media (max-width: 768px) {
  .posContainer,
  .posContainer.expandedSidebar {
    left: 0; /* Full width on mobile */
    padding: 0.25rem;
  }

  .posHeader {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.25rem;
    min-height: 50px;
  }

  .posTitle {
    font-size: 1.4rem;
  }

  .posSubtitle {
    font-size: 0.8rem;
  }

  .headerLeft {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .headerControlsSection {
    gap: 1rem;
  }

  .quickStats {
    gap: 0.5rem;
  }

  .statItem {
    padding: 0.4rem 0.6rem;
    min-width: 50px;
  }

  .statValue {
    font-size: 1rem;
  }

  .statLabel {
    font-size: 0.65rem;
  }

  .serviceGrid {
    padding: 0.5rem;
  }

  .quickEventSelector {
    padding: 0.5rem;
  }

  .quickEventTitle {
    font-size: 1.4rem;
  }

  .quickEventSubtitle {
    font-size: 0.85rem;
  }

  .categoryGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .categoryButton {
    padding: 0.75rem 0.5rem;
    min-height: 80px; /* Smaller for mobile but still touch-friendly */
    gap: 0.25rem;
  }

  .serviceTile {
    padding: 1rem;
    min-height: 100px;
  }

  .serviceGridContainer {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
  }

  .categoryIcon {
    font-size: 2rem;
  }

  .categoryName {
    font-size: 0.9rem;
  }

  .categoryCount {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }

  .quickServiceGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quickServiceCard {
    padding: 1rem;
  }

  .quickServiceName {
    font-size: 1rem;
  }

  .quickTierGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .quickTierButton {
    padding: 0.75rem 0.5rem;
    min-height: 75px; /* Touch-friendly but compact */
    font-size: 0.8rem;
  }
}

/* Extra Small Mobile Devices - Portrait */
@media (max-width: 480px) {
  .posContainer {
    padding: 0.125rem;
  }

  .posHeader {
    padding: 0.375rem 0.5rem;
    min-height: 45px;
  }

  .serviceGrid,
  .quickEventSelector {
    padding: 0.375rem;
  }

  .categoryGrid {
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 0.375rem;
  }

  .categoryButton {
    padding: 0.5rem 0.25rem;
    min-height: 70px;
    gap: 0.125rem;
  }

  .categoryIcon {
    font-size: 1.5rem;
  }

  .categoryName {
    font-size: 0.8rem;
  }

  .serviceTile {
    padding: 0.75rem;
    min-height: 90px;
  }

  .serviceGridContainer {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .serviceName {
    font-size: 1.1rem;
  }

  .serviceDescription {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .quickTierName {
    font-size: 0.8rem;
  }

  .quickTierDuration {
    font-size: 0.7rem;
  }

  .quickTierPrice {
    font-size: 1rem;
  }

  .modeToggle {
    flex-direction: row;
    gap: 0.25rem;
  }

  .modeOption {
    min-width: 80px;
    padding: 0.4rem 0.6rem;
    font-size: 0.7rem;
  }

  .modeOptionIcon {
    font-size: 0.9rem;
  }

  .modeOptionText {
    font-size: 0.7rem;
  }

  .quickPaymentSummary {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .serviceSummary {
    align-items: center;
  }

  .totalAmount {
    align-items: center;
  }

  .backToCategoriesButton {
    position: static;
    transform: none;
    margin-bottom: 1rem;
    align-self: flex-start;
  }
}

/* Tablet Responsive Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .quickEventSelector {
    padding: 1.25rem;
  }

  .categoryGrid {
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 1rem;
  }

  .quickServiceGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }

  .quickTierGrid {
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  }
}

/* Large Screen Optimizations - Full Width */
@media (min-width: 1200px) {
  .categoryGrid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    /* Remove max-width constraint for full utilization */
  }

  .quickServiceGrid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    /* Remove max-width constraint for full utilization */
  }

  .posHeader {
    padding: 1.25rem 2rem;
  }

  .headerLeft {
    gap: 3rem;
  }
}

/* Loading and Error States */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error h3 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.retryButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: #40b0a8;
}

/* Customer Form Styles */
.customerForm {
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.customerFormHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.customerFormHeader h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.customerFormHeader p {
  color: #666;
  font-size: 1rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.input {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #4ECDC4;
}

.input.inputError {
  border-color: #dc3545;
}

.errorText {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.checkboxLabel:hover {
  background-color: #f8f9fa;
}

.checkbox {
  margin-top: 0.25rem;
}

.checkboxText {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #555;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  margin-top: 1rem;
}

.skipButton {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skipButton:hover {
  background: #5a6268;
}

.privacyNote {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

/* Payment Method Selector Styles */
.paymentMethodSelector {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.paymentHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.paymentHeader h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.totalAmount {
  font-size: 1.2rem;
  color: #666;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4ECDC4;
}

.paymentMethods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.paymentMethod {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.paymentMethod:hover {
  border-color: var(--method-color, #4ECDC4);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.paymentMethod.selected {
  border-color: var(--method-color, #4ECDC4);
  background: linear-gradient(135deg, var(--method-color, #4ECDC4) 0%, rgba(78, 205, 196, 0.1) 100%);
}

.methodIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.methodContent {
  flex: 1;
  margin-bottom: 1rem;
}

.methodName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.methodDescription {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.methodFeatures {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature {
  font-size: 0.8rem;
  color: #555;
  margin-bottom: 0.25rem;
  text-align: left;
}

.methodSelector {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.radioButton {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radioButton.selected {
  border-color: var(--method-color, #4ECDC4);
}

.radioInner {
  width: 10px;
  height: 10px;
  background: var(--method-color, #4ECDC4);
  border-radius: 50%;
}

.cashInstructions,
.cardInstructions {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.instructionHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.instructionHeader h4 {
  margin: 0;
  color: #333;
}

.instructionIcon {
  font-size: 1.2rem;
}

.instructionList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.instructionList li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0e0e0;
  color: #555;
}

.instructionList li:last-child {
  border-bottom: none;
}

.securityNote {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1.5rem;
}

.securityIcon {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.securityText {
  font-size: 0.9rem;
  color: #2d5a2d;
  line-height: 1.4;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .posContainer {
    padding: 0.5rem;
  }

  .posTitle {
    font-size: 2rem;
  }

  .stepIndicator {
    gap: 1rem;
  }

  .stepLabel {
    display: none;
  }

  .serviceGrid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }

  .tierGrid {
    grid-template-columns: 1fr;
  }

  .navigationButtons {
    padding: 1rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .paymentMethods {
    grid-template-columns: 1fr;
  }
}

/* Checkout Styles */
.checkoutContainer {
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.checkoutHeader {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.checkoutHeader h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
}

.orderSummary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.orderSummary h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.serviceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.tierName {
  font-size: 0.9rem;
  color: #666;
}

.price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #4ECDC4;
}

.summaryTotal {
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  font-size: 1.3rem;
  color: #333;
}

.checkoutError {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.errorIcon {
  font-size: 1.2rem;
}

.dismissError {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #721c24;
}

.checkoutContent {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.checkoutNavigation {
  display: flex;
  justify-content: flex-start;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.processingCash {
  padding: 3rem 2rem;
  text-align: center;
}

.processingIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.processingCash h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.processingCash p {
  color: #666;
  margin-bottom: 2rem;
}

/* Square Payment Styles */
.squarePaymentContainer {
  padding: 2rem;
  max-width: 500px;
  margin: 0 auto;
}

.paymentFormHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.paymentFormHeader h4 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.paymentAmount {
  font-size: 1.1rem;
  color: #666;
}

.paymentAmount span {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4ECDC4;
}

.paymentError {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Error type specific styles */
.errorDecline {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.errorNetwork {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.errorValidation {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.errorSystem {
  background: #e2e3e5;
  border: 1px solid #d6d8db;
  color: #383d41;
}

.errorHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.errorTitle {
  font-weight: 600;
  font-size: 1rem;
  flex: 1;
  margin-left: 0.5rem;
}

.errorDismiss {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.errorDismiss:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.errorContent {
  display: block;
}

.errorText {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.errorActions {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.errorHelp {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.retryButton {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background: #c82333;
}

.cardFormContainer {
  margin: 2rem 0;
}

.cardForm {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  min-height: 100px;
  background: white;
  position: relative;
}

.cardFormPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  color: #666;
  font-style: italic;
}

.paymentActions {
  margin-top: 2rem;
}

.processPaymentButton {
  width: 100%;
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.processPaymentButton:hover:not(:disabled) {
  background: #40b0a8;
  transform: translateY(-1px);
}

.processPaymentButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.buttonSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.paymentSecurity {
  margin-top: 2rem;
  text-align: center;
}

.securityBadges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.securityBadge {
  background: #e8f5e8;
  color: #2d5a2d;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.securityText {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

/* Additional Service Grid Styles */
.noServices {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.noServices h3 {
  color: #333;
  margin-bottom: 1rem;
}

.tierCount {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
}

.serviceError {
  text-align: center;
  color: #dc3545;
}

.tierError {
  text-align: center;
  color: #dc3545;
}

.tierInfo {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

/* Billing Address Form Styles for Square AVS */
.billingAddressForm {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.billingAddressForm h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.addressGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.addressField {
  display: flex;
  flex-direction: column;
}

.addressField label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.addressField input,
.addressField select {
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out;
}

.addressField input:focus,
.addressField select:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.25);
}

.addressNote {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 12px;
  margin-top: 15px;
}

.addressNote p {
  margin: 0;
  font-size: 13px;
  color: #0066cc;
  line-height: 1.4;
}

/* Responsive adjustments for billing address */
@media (max-width: 768px) {
  .addressGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .billingAddressForm {
    padding: 15px;
    margin-top: 15px;
  }
}

/* Availability Dashboard Styles */
.availabilityDashboard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.dashboardTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.refreshButton {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.availabilityGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.timeSlotView {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.timeSlotHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.timeSlotTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dateSelector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.dateInput {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
}

.timeSlot {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
}

.timeSlot.available {
  border-color: #4caf50;
  color: #4caf50;
}

.timeSlot.available:hover {
  background: #4caf50;
  color: white;
  transform: translateY(-2px);
}

.timeSlot.busy {
  border-color: #ff9800;
  color: #ff9800;
  cursor: not-allowed;
}

.timeSlot.unavailable {
  border-color: #f44336;
  color: #f44336;
  cursor: not-allowed;
  opacity: 0.6;
}

.timeSlot.selected {
  background: #4ECDC4;
  border-color: #4ECDC4;
  color: white;
  transform: translateY(-2px);
}

.quickActions {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.quickActionsTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
}

.actionButton {
  width: 100%;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.95rem;
  font-weight: 500;
}

.actionButton:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.actionIcon {
  font-size: 1.5rem;
  color: #4ECDC4;
}

.actionContent {
  flex: 1;
  text-align: left;
}

.actionTitle {
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.actionDescription {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

/* Customer Search Component */
.customerSearch {
  position: relative;
  margin-bottom: 1.5rem;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #4ECDC4;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 1.2rem;
}

.searchResults {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.searchResult {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.searchResult:hover {
  background: #f8f9fa;
}

.searchResult:last-child {
  border-bottom: none;
}

.customerName {
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.customerDetails {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

/* Mobile Responsive Updates */
@media (max-width: 768px) {
  .posHeader {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .headerRight {
    align-items: center;
    width: 100%;
  }

  .quickStats {
    justify-content: center;
    flex-wrap: wrap;
  }

  .availabilityGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .timeSlots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .actionButton {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .actionIcon {
    font-size: 1.2rem;
  }
}

/* Selected Slot Info */
.selectedSlotInfo {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.selectedSlotInfo h4 {
  margin: 0 0 0.75rem 0;
  color: #2d5a2d;
  font-size: 1.1rem;
}

.selectedSlotInfo p {
  margin: 0.25rem 0;
  color: #2d5a2d;
  font-size: 0.9rem;
}

.selectedSlotInfo strong {
  font-weight: 600;
}

/* Search Spinner */
.searchSpinner {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}

.searchSpinner .loadingSpinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

/* Status Indicator */
.statusIndicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

/* Additional styles for new React Square Payment component */
.processingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.processingContent p {
  margin: 0;
  font-weight: 500;
  color: #333;
}

/* Payment actions and info */
.paymentActions {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.paymentInfo {
  text-align: center;
}

.paymentInfo p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.securityNote {
  color: #28a745 !important;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Processing overlay for React Square component */
.processingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 20;
}

/* Service Booking Availability Styles */
.serviceBookingAvailability {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.bookingHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.backButton {
  padding: 0.75rem 1.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.backButton:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-size: 1.8rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.serviceDescription {
  color: #6c757d;
  margin: 0;
  font-size: 1.1rem;
}

.bookingSelectionGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.selectionSection {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.selectionSection:hover {
  border-color: #4ECDC4;
}

.sectionTitle {
  font-size: 1.3rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 600;
  text-align: center;
}

.noOptions {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
}

.artistGrid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.artistCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.artistCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
}

.artistCard.selected {
  border-color: #4ECDC4;
  background: #f0fdfc;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.artistAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.artistInfo {
  flex: 1;
}

.artistName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.artistRole {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

.tierGrid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tierCard {
  padding: 1rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.tierCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
}

.tierCard.selected {
  border-color: #4ECDC4;
  background: #f0fdfc;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.tierName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.tierPrice {
  font-size: 1.3rem;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 0.25rem;
}

.tierDuration {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.tierDescription {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0;
  font-style: italic;
}

.dateSelector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.dateSelector label {
  font-weight: 500;
  color: #2c3e50;
}

.dateInput {
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.dateInput:focus {
  outline: none;
  border-color: #4ECDC4;
}

.selectPrompt {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
  background: white;
  border-radius: 8px;
  border: 2px dashed #e9ecef;
}

.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.timeSlot {
  padding: 0.5rem;
  text-align: center;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 2px solid;
  transition: all 0.3s ease;
}

.timeSlot.available {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.timeSlot.available.clickable {
  cursor: pointer;
}

.timeSlot.available.clickable:hover {
  background: #c3e6cb;
  transform: translateY(-2px);
}

.timeSlot.busy {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
  cursor: not-allowed;
}

.timeSlot.unavailable {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
  cursor: not-allowed;
  opacity: 0.6;
}

.timeSlot.selected {
  background: #4ECDC4;
  border-color: #4ECDC4;
  color: white;
  transform: translateY(-2px);
}

.selectionSummary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #4ECDC4;
}

.selectionSummary h4 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.summaryItem {
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summaryItem strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.25rem;
}

.nextStep {
  text-align: center;
  color: #4ECDC4;
  font-weight: 500;
  margin: 0;
  font-style: italic;
}

/* Update order summary styles for new layout */
.orderSummary .artistName {
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

.orderSummary .timeSlot {
  font-size: 0.9rem;
  color: #4ECDC4;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .bookingSelectionGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .selectionSection {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .serviceBookingAvailability {
    padding: 1rem;
  }
  
  .bookingHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .backButton {
    align-self: flex-start;
  }
  
  .timeSlots {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }
  
  .summaryGrid {
    grid-template-columns: 1fr;
  }
}

/* =============================================
   SQUARE TERMINAL INTEGRATION STYLES
   ============================================= */

/* Terminal Payment Container */
.terminalPaymentContainer {
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.terminalHeader {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.terminalHeader h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.paymentAmount {
  font-size: 2.5rem;
  font-weight: 700;
  color: #006AFF;
  margin: 0;
}

/* Device Selection */
.deviceSelection {
  margin-bottom: 2rem;
}

.deviceSelection h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.deviceList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.deviceCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.deviceCard:hover {
  border-color: #006AFF;
  box-shadow: 0 4px 12px rgba(0, 106, 255, 0.15);
}

.deviceCard.selected {
  border-color: #006AFF;
  background: linear-gradient(135deg, #006AFF 0%, #0052CC 100%);
  color: white;
}

.deviceIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.deviceCard.selected .deviceIcon {
  background: rgba(255, 255, 255, 0.2);
}

.deviceInfo {
  flex: 1;
}

.deviceName {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.deviceStatus {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

/* Processing State */
.processingState {
  text-align: center;
  padding: 2rem;
}

.statusIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

.statusMessage {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.terminalInstructions {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.terminalInstructions p {
  margin: 0 0 0.5rem 0;
  color: #666;
  line-height: 1.5;
}

.terminalInstructions p:last-child {
  margin-bottom: 0;
}

/* No Devices State */
.noDevicesState {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.noDevicesIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.noDevicesState h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.noDevicesState p {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
}

/* Terminal Actions */
.terminalActions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.startPaymentButton {
  background: linear-gradient(135deg, #006AFF 0%, #0052CC 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 106, 255, 0.3);
}

.startPaymentButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 106, 255, 0.4);
}

.startPaymentButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Enhanced Payment Method Selector for Terminal */
.paymentMethodSelector .deviceInfo {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.85rem;
}

.deviceLabel {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  display: block;
}

.deviceItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.deviceItem:last-child {
  margin-bottom: 0;
}

.deviceStatusText {
  color: #666;
  font-size: 0.8rem;
}

.moreDevices {
  color: #666;
  font-style: italic;
  font-size: 0.8rem;
}

.noDevices {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc3545;
  font-size: 0.85rem;
}

.unavailableReason {
  color: #dc3545;
  font-size: 0.8rem;
  font-style: italic;
  margin-top: 0.5rem;
}

.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

/* Terminal Instructions */
.terminalInstructions {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #006AFF;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.terminalDeviceStatus {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.terminalDeviceStatus h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.deviceStatusItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.deviceStatusItem:last-child {
  margin-bottom: 0;
}

/* Device Manager Styles */
.deviceManager {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.deviceManagerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.deviceManagerHeader h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.addDeviceButton {
  background: linear-gradient(135deg, #006AFF 0%, #0052CC 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.addDeviceButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 106, 255, 0.3);
}

.addDeviceButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Create Device Form */
.createDeviceForm {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 2px solid #e9ecef;
}

.createDeviceForm h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.formGroup input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.formGroup input:focus {
  outline: none;
  border-color: #006AFF;
}

.formActions {
  display: flex;
  gap: 1rem;
}

.createButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.createButton:hover {
  background: #218838;
}

.createButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Device Manager Additional Styles */
.deviceManagerFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.statusLegend {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.statusLegend span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.refreshButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshButton:hover {
  background: #218838;
  transform: translateY(-1px);
}

.refreshButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Device Card Styles */
.deviceCard {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.deviceCard:hover {
  border-color: #006AFF;
  box-shadow: 0 4px 12px rgba(0, 106, 255, 0.15);
}

.deviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.deviceName {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.deviceStatus {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.deviceDetails {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.deviceCode {
  margin-bottom: 1rem;
  font-size: 1rem;
}

.codeValue {
  background: #fff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 2px solid #e9ecef;
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  font-weight: 600;
  color: #006AFF;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  margin-left: 0.5rem;
}

.codeValue:hover {
  border-color: #006AFF;
  background: #f0f8ff;
}

.deviceInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.infoLabel {
  font-size: 0.85rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

.pairingInstructions {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #006AFF;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.pairingInstructions h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.75rem 0;
}

.pairingInstructions ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #555;
}

.pairingInstructions li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.pairingInstructions li:last-child {
  margin-bottom: 0;
}

.pairingInstructions strong {
  color: #006AFF;
  font-weight: 700;
}

/* Error Message Styles */
.errorMessage {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.errorIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.errorText {
  flex: 1;
  font-size: 1rem;
  line-height: 1.4;
}

.dismissError {
  background: none;
  border: none;
  color: #721c24;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.dismissError:hover {
  background: rgba(114, 28, 36, 0.1);
}

/* Loading States */
.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #006AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingState p {
  font-size: 1.1rem;
  margin: 0;
}

/* =============================================
   SQUARE READER INTEGRATION STYLES
   ============================================= */

/* Reader Payment Container */
.readerPaymentContainer {
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.readerHeader {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.readerHeader h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

/* Reader Selection */
.readerSelection {
  margin-bottom: 2rem;
}

.readerSelection h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.readerList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.readerCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.readerCard:hover {
  border-color: #FF6B35;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.readerCard.selected {
  border-color: #FF6B35;
  background: linear-gradient(135deg, #FF6B35 0%, #E55A2B 100%);
  color: white;
}

.readerIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.readerCard.selected .readerIcon {
  background: rgba(255, 255, 255, 0.2);
}

.readerInfo {
  flex: 1;
}

.readerName {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.readerType {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0 0 0.25rem 0;
}

.readerStatus {
  font-size: 0.85rem;
  opacity: 0.9;
  margin: 0;
}

/* Reader Instructions */
.readerInstructions {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 1px solid #FF6B35;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.readerInstructions p {
  margin: 0 0 0.5rem 0;
  color: #666;
  line-height: 1.5;
}

.readerInstructions p:last-child {
  margin-bottom: 0;
}

.readerDeviceStatus {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.readerDeviceStatus h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

/* Install Instructions */
.installInstructions {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.installInstructions h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.installInstructions ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #555;
}

.installInstructions li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.installInstructions li:last-child {
  margin-bottom: 0;
}

/* Callback Page Styles */
.callbackContainer {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  text-align: center;
}

.processingCallback {
  padding: 3rem 2rem;
}

.processingCallback h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 1rem 0;
}

.processingCallback p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.5;
}

.successCallback {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 2px solid #28a745;
  border-radius: 12px;
}

.successIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.successCallback h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #28a745;
  margin: 1rem 0;
}

.resultDetails {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  text-align: left;
}

.resultItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.resultItem:last-child {
  border-bottom: none;
}

.resultLabel {
  font-weight: 600;
  color: #333;
}

.resultValue {
  font-family: 'Courier New', monospace;
  color: #666;
  font-size: 0.9rem;
}

.redirectMessage {
  font-size: 1rem;
  color: #666;
  margin: 1rem 0;
  font-style: italic;
}

.returnButton {
  background: linear-gradient(135deg, #28a745 0%, #218838 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.returnButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.errorCallback {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8e8e8 0%, #fdf0f0 100%);
  border: 2px solid #dc3545;
  border-radius: 12px;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.errorCallback h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #dc3545;
  margin: 1rem 0;
}

.errorMessage {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  color: #721c24;
  font-weight: 500;
}

.sessionInfo {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 2rem;
  text-align: left;
}

.sessionInfo h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.sessionDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.sessionItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sessionItem span:first-child {
  font-size: 0.85rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sessionItem span:last-child {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

/* Device Section Styles */
.deviceSection {
  margin-bottom: 2rem;
}

.deviceSectionTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.deviceTypeLabel {
  background: #f8f9fa;
  color: #666;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Mobile Responsiveness for Terminal and Reader */
@media (max-width: 768px) {
  .terminalPaymentContainer,
  .readerPaymentContainer {
    padding: 1rem;
    margin: 0;
  }

  .terminalHeader h3,
  .readerHeader h3 {
    font-size: 1.5rem;
  }

  .paymentAmount {
    font-size: 2rem;
  }

  .deviceManagerHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .formActions {
    flex-direction: column;
  }

  .deviceInfo {
    grid-template-columns: 1fr;
  }

  .deviceManagerFooter {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .statusLegend {
    justify-content: center;
  }

  .deviceCard,
  .readerCard {
    padding: 1rem;
  }

  .deviceHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .codeValue {
    display: block;
    margin-left: 0;
    margin-top: 0.5rem;
    text-align: center;
  }

  .callbackContainer {
    margin: 1rem;
    padding: 1rem;
  }

  .resultDetails {
    padding: 1rem;
  }

  .sessionDetails {
    grid-template-columns: 1fr;
  }
}

/* =============================================
   TERMINAL STATUS MONITOR STYLES
   ============================================= */

/* Full Status Monitor */
.terminalStatusMonitor {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.monitorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.monitorHeader h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.monitorControls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.lastUpdateInfo {
  font-size: 0.9rem;
  color: #666;
}

.deviceStatusList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.deviceStatusCard {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.deviceStatusCard:hover {
  border-color: #006AFF;
  box-shadow: 0 2px 8px rgba(0, 106, 255, 0.15);
}

.deviceStatusHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.deviceStatusDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.statusDetail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailLabel {
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailValue {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.monitorFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  font-size: 0.9rem;
  color: #666;
}

.statusSummary {
  display: flex;
  gap: 1rem;
}

.statusSummary span {
  padding: 0.25rem 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-weight: 500;
}

.autoRefreshInfo {
  font-style: italic;
}

/* Compact Status Monitor */
.terminalStatusCompact {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.9rem;
}

.compactHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.statusIndicator {
  font-size: 1rem;
}

.statusText {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.compactRefreshButton {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.compactRefreshButton:hover {
  background: #f8f9fa;
  color: #333;
}

.compactRefreshButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.compactDeviceList {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.compactDeviceItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
}

.deviceStatusIcon {
  font-size: 0.8rem;
}

.deviceNameShort {
  font-weight: 500;
  color: #333;
}

.lastUpdateText {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
  margin-top: 0.25rem;
}

/* Status Indicator Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.compactRefreshButton[disabled] {
  animation: pulse 1s infinite;
}

/* Mobile Responsiveness for Status Monitor */
@media (max-width: 768px) {
  .monitorHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .monitorControls {
    justify-content: space-between;
  }

  .deviceStatusDetails {
    grid-template-columns: 1fr;
  }

  .monitorFooter {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .statusSummary {
    justify-content: center;
  }

  .compactDeviceList {
    justify-content: center;
  }
}
