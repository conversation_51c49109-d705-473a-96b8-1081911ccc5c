import { createClient } from '@supabase/supabase-js'
import { safeSerializeData } from '@/lib/safe-render-utils'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * API endpoint for fetching services with available artists for POS Terminal
 * GET /api/admin/pos/services-with-artists
 * 
 * Returns services that have available artists, with artist information
 * for the multi-artist booking system
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    console.log('🏪 Fetching services with available artists for POS Terminal...')

    // Fetch services with available artists using the view
    // Include services visible on POS OR events (for Quick Event Mode)
    const { data: servicesData, error: servicesError } = await supabase
      .from('services_with_available_artists')
      .select('*')
      .eq('status', 'active')
      .or('visible_on_pos.eq.true,visible_on_events.eq.true')
      .gt('available_artist_count', 0) // Only services with available artists
      .order('name')

    if (servicesError) {
      console.error('Error fetching services with artists:', servicesError)
      return res.status(500).json({ error: 'Failed to fetch services' })
    }

    // Also fetch pricing tiers for each service
    const serviceIds = servicesData?.map(s => s.id) || []
    
    let pricingTiers = []
    if (serviceIds.length > 0) {
      const { data: tiersData, error: tiersError } = await supabase
        .from('service_pricing_tiers')
        .select('*')
        .in('service_id', serviceIds)
        .order('sort_order')

      if (tiersError) {
        console.warn('Error fetching pricing tiers:', tiersError)
      } else {
        pricingTiers = tiersData || []
      }
    }

    // Transform and serialize data for POS Terminal
    const transformedServices = (servicesData || []).map(service => {
      // Get pricing tiers for this service
      const serviceTiers = pricingTiers.filter(tier => tier.service_id === service.id)
      
      // Parse available artists (stored as JSON in the view)
      let availableArtists = []
      try {
        availableArtists = Array.isArray(service.available_artists) 
          ? service.available_artists 
          : JSON.parse(service.available_artists || '[]')
      } catch (e) {
        console.warn('Error parsing available_artists for service:', service.id, e)
        availableArtists = []
      }

      // Calculate price range from pricing tiers or fallback to base price
      const getPriceRange = () => {
        if (serviceTiers.length > 0) {
          const prices = serviceTiers.map(tier => parseFloat(tier.price || 0))
          const minPrice = Math.min(...prices)
          const maxPrice = Math.max(...prices)
          
          if (minPrice === maxPrice) {
            return `$${minPrice.toFixed(2)}`
          }
          return `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`
        }
        return `$${parseFloat(service.price || 0).toFixed(2)}`
      }

      // Get category icon
      const getCategoryIcon = (category) => {
        const iconMap = {
          'painting': '🎨',
          'airbrush': '🎨', 
          'braiding': '💇',
          'hair': '💇',
          'glitter': '✨',
          'sparkle': '✨',
          'special': '🎭',
          'uv': '🌟',
          'face painting': '🎨',
          'body art': '🎨'
        }
        return iconMap[category?.toLowerCase()] || '🎨'
      }

      return {
        // Service basic info (all converted to strings for React safety)
        id: String(service.id || ''),
        name: String(service.name || ''),
        description: String(service.description || ''),
        duration: Number(service.duration) || 0,
        price: String(service.price || '0'),
        color: String(service.color || '#6a0dad'),
        category: String(service.category || ''),
        image_url: String(service.image_url || '/images/placeholder.svg'),
        status: String(service.status || 'active'),
        featured: Boolean(service.featured),

        // Visibility flags for Quick Event Mode filtering
        visible_on_pos: Boolean(service.visible_on_pos),
        visible_on_events: Boolean(service.visible_on_events),
        
        // POS-specific data
        priceRange: getPriceRange(),
        icon: getCategoryIcon(service.category),
        availableArtistCount: Number(service.available_artist_count) || 0,
        
        // Available artists (serialized for React safety)
        availableArtists: availableArtists.map(artist => ({
          id: String(artist.artist_id || ''),
          name: String(artist.artist_name || ''),
          displayName: String(artist.display_name || artist.artist_name || ''),
          skillLevel: String(artist.skill_level || 'intermediate'),
          customRate: artist.custom_rate ? String(artist.custom_rate) : null,
          isPrimaryService: Boolean(artist.is_primary_service),
          isAvailableToday: Boolean(artist.is_available_today)
        })),
        
        // Pricing tiers (serialized for React safety)
        pricing_tiers: serviceTiers.map(tier => ({
          id: String(tier.id || ''),
          name: String(tier.name || ''),
          description: String(tier.description || ''),
          duration: String(tier.duration || '0'),
          price: String(tier.price || '0'),
          is_default: Boolean(tier.is_default),
          sort_order: Number(tier.sort_order) || 0
        }))
      }
    })

    // Final serialization to ensure no complex objects
    const serializedServices = safeSerializeData(transformedServices)

    console.log(`✅ Fetched ${serializedServices.length} services with available artists`)
    console.log('Services with artist counts:', serializedServices.map(s => ({
      name: s.name,
      artistCount: s.availableArtistCount,
      artists: s.availableArtists.map(a => a.displayName)
    })))

    return res.status(200).json({ 
      services: serializedServices,
      totalServices: serializedServices.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in POS services-with-artists API:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}
